# Performance Testing Infrastructure

This Ansible framework provides automated deployment and management of performance testing infrastructure using Locust for comprehensive load testing scenarios.

## 📊 Workflow Diagram

For a visual representation of the complete workflow, see: **[Workflow Diagram](workflow-diagram.md)**

## Features

- **Automated Infrastructure Deployment**: Complete setup of performance testing environment
- **Locust Integration**: Built-in support for Locust load testing framework
- **Flexible Configuration**: Support for multiple testing scenarios via extra-vars
- **Scalable Architecture**: Support for distributed testing with generator nodes
- **Environment Isolation**: Separate preparation and deployment phases
- **Conda Environment Management**: Automated Python environment setup for testing tools
- **Three-Phase Deployment**: Infrastructure preparation, generator deployment, and test execution
- **Multiple Run Types**: Support for start, stop, and backup operations
- **Debug Mode**: Real-time container log monitoring for troubleshooting

## Directory Structure

```
projects/performance/
├── ansible.cfg                              # Ansible configuration
├── site.yml                                 # Main playbook
├── README.md                                # This documentation
├── workflow-diagram.md                      # Visual workflow diagram
├── vault_pass.txt                          # Vault password file
├── inventory/                               # Inventory files for target hosts
│   └── servers.yml                         # Server definitions
├── keys/                                    # SSH keys for authentication
├── extra-vars/                             # External variable configurations
│   └── locust/                             # Locust-specific configurations
│       ├── locust-standalone-enquete.yml   # Enquete scenario configuration
│       └── locust-standalone-pres.yml      # Presentation scenario configuration
└── roles/
    ├── Dep.SetFacts/                       # Global variable management
    │   ├── tasks/main.yml
    │   └── vars/main.yml                   # Performance testing variables
    ├── Dep.SshConnection/                  # SSH connection management
    │   └── tasks/
    │       ├── main.yml
    │       └── task-known_hosts.yml
    ├── Dep.RepoOps/                        # Repository operations
    │   └── tasks/
    │       ├── main.yml
    │       ├── task-git-config.yml
    │       └── task-sync-repo.yml
    ├── 01-prepare/                         # Infrastructure preparation
    │   ├── meta/main.yml                   # Role dependencies
    │   ├── tasks/
    │   │   ├── main.yml
    │   │   ├── task-apt_update_upgrade.yml
    │   │   ├── task-install_docker_dep.yml
    │   │   └── task-install_pip3.yml
    │   └── handlers/main.yml
    ├── 02-generator/                       # Generator service deployment
    │   ├── meta/main.yml
    │   ├── tasks/
    │   │   ├── main.yml
    │   │   ├── task-config-generator.yml
    │   │   └── task-build-locust.yml
    │   ├── templates/
    │   │   └── auto-install-miniconda3.sh.j2
    │   └── handlers/main.yml
    └── 03-locust/                          # Locust testing framework
        ├── tasks/
        │   ├── main.yml
        │   └── locust-standalone.yml
        └── vars/main.yml                   # Locust configuration variables
```

## Prerequisites

1. Ansible installed on the control machine
2. SSH access configured to target generator servers
3. Inventory file configured with generator groups
4. SSH keys set up for authentication
5. Target servers with sufficient resources for load testing

## Workflow Overview

The Performance Testing Infrastructure follows a three-phase approach:

### Phase 1: Infrastructure Preparation (01-prepare)
- **Purpose**: Set up the basic infrastructure and dependencies
- **Components**: System updates, Docker installation, Python pip, Miniconda3
- **Usage**: `ansible-playbook site.yml -t play_prepare`

### Phase 2: Generator Service Deployment (02-generator)
- **Purpose**: Deploy and configure the test generator services
- **Components**: Conda environment setup, Locust Docker image building
- **Usage**: `ansible-playbook site.yml -t play_generator`

### Phase 3: Locust Performance Testing (03-locust)
- **Purpose**: Execute actual performance tests with various configurations
- **Components**: Test execution, monitoring, result management
- **Run Types**:
  - **Start**: `ansible-playbook site.yml -t locust_run -e "run_type=start"`
  - **Stop**: `ansible-playbook site.yml -t locust_run -e "run_type=stop"`
  - **Backup**: `ansible-playbook site.yml -t locust_run -e "run_type=backup"`

### Testing Modes
- **Standalone Mode**: Single-node testing for smaller loads
- **Distributed Mode**: Multi-node coordinated testing (planned feature)

## Usage

### Display Built-in Usage Instructions

The playbook includes comprehensive built-in usage instructions that can be displayed using:

```bash
ansible-playbook site.yml --tags usage
```

This command will display:
- **Available top-level tags**: `play_prepare`, `play_generator`, `locust_run`, `deploy_all`
- **Locust task execution details**: How to use the `03-locust` role with `run_type` variable
- **Required variables**: `run_type` (start/stop/backup) and scenario definition files
- **Optional variables**: `standalone.sync_data`, `standalone.build_image`, `standalone.debug`
- **Built-in examples**: Ready-to-use command examples for different scenarios

### Infrastructure Deployment

Navigate to the performance directory before executing any commands:

```bash
cd projects/performance
```

#### Complete Deployment

Deploy all performance services (preparation + generator):

```bash
# All-in-one command: deploy all components
ansible-playbook site.yml -t deploy_all
```

#### Targeted Deployment

Deploy specific components individually:

```bash
# Infrastructure preparation only
ansible-playbook site.yml -t play_prepare

# Generator services only
ansible-playbook site.yml -t play_generator
```

### Locust Load Testing

#### Configuration Priority

Variable priority (highest to lowest):
1. Command-line specified variables (highest priority)
2. Extra-vars files (`extra-vars/locust/*.yml`)
3. Role default variables (`roles/03-locust/vars/main.yml`)

#### Standalone Mode Execution

Execute Locust in standalone mode with specific configuration:

```bash
# Using specific extra-vars configuration file
ansible-playbook site.yml -t locust_run -e "@extra-vars/locust/locust-standalone-enquete.yml"

# Alternative: Direct tag specification
ansible-playbook site.yml -t global,known_hosts,sync_repo,task_build_locust,standalone -e "@extra-vars/locust/locust-standalone-pres.yml"
```

#### Available Scenarios

- **Enquete Scenario**: `extra-vars/locust/locust-standalone-enquete.yml`
- **Presentation Scenario**: `extra-vars/locust/locust-standalone-pres.yml`

### Examples

#### Complete Deployment Examples
```bash
# Deploy complete performance testing environment
ansible-playbook site.yml -t deploy_all

# Deploy with specific scenario configuration
ansible-playbook site.yml -t deploy_all -e "@extra-vars/locust/locust-standalone-enquete.yml"
```

#### Phase-by-Phase Deployment Examples
```bash
# Phase 1: Prepare infrastructure only
ansible-playbook site.yml -t play_prepare

# Phase 2: Deploy generator services
ansible-playbook site.yml -t play_generator

# Phase 3: Execute Locust tests
ansible-playbook site.yml -t locust_run -e "@extra-vars/locust/locust-standalone-enquete.yml"
```

#### Locust Testing Examples
```bash
# Start a test with enquete scenario
ansible-playbook site.yml -t locust_run,standalone -e "@extra-vars/locust/locust-standalone-enquete.yml" -e "run_type=start"

# Start a test with presentation scenario and debugging
ansible-playbook site.yml -t locust_run,standalone -e "@extra-vars/locust/locust-standalone-pres.yml" -e "run_type=start" -e "standalone.debug=true"

# Start a test with data sync and image rebuild
ansible-playbook site.yml -t locust_run,standalone -e "@extra-vars/locust/locust-standalone-enquete.yml" -e "run_type=start" -e "standalone.sync_data=true" -e "standalone.build_image=true"

# Stop a running test
ansible-playbook site.yml -t locust_run,standalone -e "@extra-vars/locust/locust-standalone-enquete.yml" -e "run_type=stop"

# Backup test results
ansible-playbook site.yml -t locust_run,standalone -e "@extra-vars/locust/locust-standalone-pres.yml" -e "run_type=backup"
```

#### Advanced Configuration Examples
```bash
# Override default variables from command line
ansible-playbook site.yml -t locust_run,standalone -e "@extra-vars/locust/locust-standalone-enquete.yml" -e "run_type=start" -e "standalone.scenario_data.relative_exec_scene_dir=scenarios/CustomTest/*"

# Display usage instructions
ansible-playbook site.yml --tags usage
```

## Built-in Usage Instructions Details

When you run `ansible-playbook site.yml --tags usage`, the system displays the following comprehensive guide:

### Available Top-Level Tags
- **`play_prepare`**: Installs required dependencies on the generator
- **`play_generator`**: Deploys and configures the test generator services
- **`locust_run`**: Executes Locust tasks (start, stop, backup)
- **`deploy_all`**: Runs both play_prepare and play_generator

### Locust Task Execution
The `03-locust` role is controlled by the `run_type` variable:

#### Required Variables
- **`run_type`**: Defines the action. Can be 'start', 'stop', or 'backup'
- **`extra-vars`**: You must provide a scenario definition file from `extra-vars/locust/`

#### Optional Boolean Variables (for 'start' run_type)
- **`standalone.sync_data`**: (default: false) Set to true to sync test data from the repo
- **`standalone.build_image`**: (default: false) Set to true to rebuild the locust docker image
- **`standalone.debug`**: (default: false) Set to true to display live container logs

#### Available Tags for Locust Tasks
- **`standalone`** / **`locust_standalone`**: To run locust in standalone mode

### Built-in Examples from Usage Instructions
```bash
# Run all preparation and deployment steps
ansible-playbook site.yml -t deploy_all -e "@extra-vars/locust/locust-standalone-sde.yml"

# Using the extra-vars file directly in the command line to start a standalone locust test
ansible-playbook site.yml -t locust_run,standalone -e "@extra-vars/locust/locust-standalone-sde.yml"

# Start a standalone locust test with debugging enabled
ansible-playbook site.yml -t locust_run,standalone -e "@extra-vars/locust/locust-standalone-sde.yml" -e "run_type=start" -e "standalone.debug=true"

# Stop a running test
ansible-playbook site.yml -t locust_run,standalone -e "@extra-vars/locust/locust-standalone-sde.yml" -e "run_type=stop"

# Backup test results
ansible-playbook site.yml -t locust_run,standalone -e "@extra-vars/locust/locust-standalone-sde.yml" -e "run_type=backup"
```

## Configuration

### Variables

Key configuration variables are defined in:
- `roles/Dependencies/SetFacts/vars/main.yml`: Global performance testing variables
- `roles/03-locust/vars/main.yml`: Default Locust configuration
- `extra-vars/locust/*.yml`: Scenario-specific overrides

### Inventory Groups

Configure your inventory file with appropriate generator groups:

```ini
[generator]
generator1.performance.local
generator2.performance.local

[locust]
locust1.performance.local
locust2.performance.local
```

### Extra Variables Files

Create custom scenario configurations in `extra-vars/locust/`:

```yaml
# Example: custom-scenario.yml
locust:
  target_host: "https://your-target-server.com"
  users: 100
  spawn_rate: 10
  run_time: "5m"
```

## Roles Description

### 01-prepare
- System updates and upgrades
- Docker installation and dependencies
- Pip3 installation
- Basic infrastructure preparation

### 02-generator
- Generator service deployment
- Conda environment setup
- Key management
- Service configuration

### 03-locust
- Locust framework installation
- Test scenario configuration
- Load testing execution
- Results collection

## Load Testing Best Practices

1. **Resource Planning**: Ensure generator servers have sufficient CPU and memory
2. **Network Considerations**: Monitor network bandwidth during testing
3. **Baseline Testing**: Start with small loads and gradually increase
4. **Monitoring**: Monitor both generator and target system metrics
5. **Test Duration**: Plan appropriate test durations for meaningful results
6. **Environment Isolation**: Use dedicated testing environments

## Monitoring and Logging

### Test Execution Logs

- Ansible execution logs: `./ansible.log`
- Locust test results: Check generator servers for detailed results
- System metrics: Monitor CPU, memory, and network usage

### Performance Metrics

Monitor the following during test execution:
- Response times
- Throughput (requests per second)
- Error rates
- Resource utilization on target systems

## Troubleshooting

### Common Issues

1. **Generator Connection Issues**: Verify SSH keys and inventory configuration
2. **Conda Environment Errors**: Check Python version compatibility
3. **Locust Startup Failures**: Verify target host accessibility
4. **Resource Constraints**: Monitor system resources on generator nodes

### Debug Mode

Run with verbose output for troubleshooting:

```bash
ansible-playbook site.yml -t deploy_all -vvv
```

### Validation Commands

```bash
# Check generator status
ansible generator -m ping

# Verify Locust installation
ansible generator -m shell -a "locust --version"

# Check conda environment
ansible generator -m shell -a "conda info --envs"
```

## Security Considerations

- Secure SSH keys and limit access to generator servers
- Use isolated networks for performance testing
- Avoid testing production systems without proper authorization
- Monitor and log all testing activities
- Implement proper cleanup procedures after testing

## Scaling Performance Tests

### Distributed Testing

For large-scale testing:
1. Deploy multiple generator nodes
2. Configure Locust in distributed mode
3. Use load balancers for result aggregation
4. Monitor resource usage across all nodes

### Resource Optimization

- Optimize test scripts for efficiency
- Use appropriate spawn rates
- Monitor and tune JVM/Python settings
- Consider using dedicated testing hardware

## Contributing

When adding new functionality:

1. Follow existing role structure and naming conventions
2. Add comprehensive documentation for new scenarios
3. Test thoroughly with different load patterns
4. Update configuration examples
5. Consider backward compatibility with existing scenarios

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review Ansible and Locust documentation
3. Validate configuration files and inventory
4. Monitor system logs for detailed error information